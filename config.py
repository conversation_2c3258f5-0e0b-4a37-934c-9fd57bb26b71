from models import ModelProvider
from agent import ModelConfig
from os import getenv

# Definieer de modellen als globale variabelen
chat_model = ModelConfig(
    provider=ModelProvider.LMSTUDIO,
    name="deepseek-coder-6.7b-instruct",
    kwargs={
        "temperature": 0.2,
        "base_url": getenv("LM_STUDIO_BASE_URL")
    }
)

utility_model = ModelConfig(
    provider=ModelProvider.LMSTUDIO,
    name="deepseek-coder-6.7b-instruct",
    kwargs={
        "temperature": 0.2,
        "base_url": getenv("LM_STUDIO_BASE_URL")
    }
)

# Gebruik de globale variabelen in de functie
def initialize_models():
    return chat_model, utility_model

def initialize_dynamic_models():
    """
    Dynamisch modellen initialiseren op basis van configuratie in .env.
    """
    models = {
        "chat_model": ModelConfig(
            provider=ModelProvider.LMSTUDIO,
            name="deepseek-coder-6.7b-instruct",
            kwargs={
                "temperature": 0.2,
                "base_url": getenv("LM_STUDIO_BASE_URL")
            }
        ),
        "utility_model": ModelConfig(
            provider=ModelProvider.LMSTUDIO,
            name="deepseek-coder-6.7b-instruct",
            kwargs={
                "temperature": 0.2,
                "base_url": getenv("LM_STUDIO_BASE_URL")
            }
        )
    }
    return models