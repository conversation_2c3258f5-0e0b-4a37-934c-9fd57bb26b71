from models import ModelProvider
from agent import ModelConfig
from os import getenv

# Definieer de modellen als globale variabelen
chat_model = ModelConfig(
    provider=ModelProvider.OLLAMA,
    name="llama3.1:8b",
    kwargs={
        "temperature": 0.2
    }
)

utility_model = ModelConfig(
    provider=ModelProvider.OLLAMA,
    name="llama3.2:3b",
    kwargs={
        "temperature": 0.2
    }
)

# Gebruik de globale variabelen in de functie
def initialize_models():
    return chat_model, utility_model

def initialize_dynamic_models():
    """
    Dynamisch modellen initialiseren op basis van configuratie in .env.
    """
    models = {
        "chat_model": ModelConfig(
            provider=ModelProvider.OLLAMA,
            name="llama3.1:8b",
            kwargs={
                "temperature": 0.2
            }
        ),
        "utility_model": ModelConfig(
            provider=ModelProvider.OLLAMA,
            name="llama3.2:3b",
            kwargs={
                "temperature": 0.2
            }
        )
    }
    return models
