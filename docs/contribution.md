# Contributing to Agent Zero

Contributions to improve Agent Zero are very welcome!  This guide outlines how to contribute code, documentation, or other improvements.

## Getting Started

1. **Fork the Repository:** Fork the Agent Zero repository on GitHub.
2. **Clone Your Fork:** Clone your forked repository to your local machine.
3. **Create a Branch:** Create a new branch for your changes. Use a descriptive name that reflects the purpose of your contribution (e.g., `fix-memory-leak`, `add-search-tool`, `improve-docs`).

## Making Changes

* **Code Style:** Follow the existing code style. Agent Zero generally follows PEP 8 conventions.
* **Documentation:**  Update the documentation if your changes affect user-facing functionality. The documentation is written in Markdown.
* **Commit Messages:**  Write clear and concise commit messages that explain the purpose of your changes.

## Submitting a Pull Request

1. **Push Your Branch:** Push your branch to your forked repository on GitHub.
2. **Create a Pull Request:** Create a pull request from your branch to the appropriate branch in the main Agent Zero repository.
   * Target the `development` branch.
3. **Provide Details:** In your pull request description, clearly explain the purpose and scope of your changes. Include relevant context, test results, and any other information that might be helpful for reviewers.
4. **Address Feedback:**  Be responsive to feedback from the community. We love changes, but we also love to discuss them!

## Documentation Stack

- The documentation is built using Markdown. We appreciate your contributions even if you don't know Markdown, and look forward to improve Agent Zero for everyone's benefit.